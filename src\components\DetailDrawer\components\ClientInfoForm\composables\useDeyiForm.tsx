import { FiledOptions, validators } from '@/components/Form';
import type {
  DynamicFormInstance,
  DynamicFormConfig,
} from '@/components/Form/src/types/dynamicForm';
import { ref, computed, ComputedRef } from 'vue';
import { cloneDeep } from 'lodash-es';
import CitySelect from '@/components/CitySelect/index.vue';
import CitySelectV2 from '@/components/CitySelect/v2.vue';
import { validPhone, validIdCard } from '@/utils/formValidationRules';
import {
  type IUpdateCustomerModalFormModel,
  educationLevelOptions,
  maritalStatusOptions,
  houseTypeOptions,
  companyNatureOptions,
  contactRelationOptions,
  contactRelationOptions1,
  // childCountOptions,
  occupationOptions,
  workTypeOptions,
  loanPurposeOptions,
} from '@/components/DetailDrawer/components/ClientInfoForm/types/deyiForm';

export function useDeyiForm() {
  // 表单引用
  const linkageFormRef = ref<DynamicFormInstance>();
  const DEFAULT_FORM_MODEL: IUpdateCustomerModalFormModel = {
    clueId: null,
    personalInfo: {
      id: null,
      loanPurpose: null, //申请用途新增
      name: '',
      cityCode: null,
      cityName: '',
      provinceName: '',
      provinceCode: null,
      educationLevel: null,
      maritalStatus: null,
      mateName: '', //配偶姓名
      mateMobile: '', //配偶手机号
      mateIdCardNumber: '', //配偶身份证号
      // mateWorkCompany: '', //配偶工作单位
      // mateWorkCompanyCity: '', //配偶工作单位地址
      // mateWorkCompanyAddress: '', //配偶工作单位详细地址
      // childCount: null,
      residenceAddress: '',
      houseType: null,
      company: '',
      companyCityCode: '', //单位地址新增
      companyAddress: '',
      companyPhone: null,
      companyNature: null,
      monthlyIncome: null,
      workType: null, //从事行业种类
      occupation: null, //职业
      //联系人关系
      contactRelation1: null,
      contactName1: '',
      contactPhone1: null,
      contactRelation2: null,
      contactName2: '',
      contactPhone2: null,
      clueId: null,
      idCardNumber: null,
    },
    certificateInfo: {
      id: null,
      idCardImgUrl: '',
      idCardImgBackUrl: '',
      drivingLicenseImgUrl: '',
      // drivingLicenseImgBackUrl: '',
      paperDriverLicenseImgUrl: '',
      // paperDriverLicenseImgBackUrl: '',
      // bankCardImgUrl: '',
      // bankCardBackUrl: '',
      odographImgUrl: '', //里程表 新增
      vehicleRegistrationImgUrl: '', //车辆登记证(2) 新增
      //机动车状态（12123截图）
      vehicleStatusImgUrl: '', //机动车状态（12123截图）
      clueId: null,
    },
    otherDocuments: {
      id: null,
      vehiclePhotosLeft45: null,
      vehiclePhotoRight45: null,
      //人车合影
      vehiclePhotoPeopleCar: null,
      //车头全景
      // vehiclePhotoFront: null,/
      clueId: null,
    },
  };

  // 表单数据
  const linkageFormData = ref<any>(cloneDeep(DEFAULT_FORM_MODEL));
  // 联动表单配置
  const linkageFormConfig: ComputedRef<DynamicFormConfig> = computed(() => ({
    labelWidth: 120,
    columns: 24,
    showActionButtons: false,
    labelPlacement: 'left',
    onSubmit: async (_data) => {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      //成功之后的处理
      linkageFormRef.value?.resetForm();
    },
    fields: [
      // 个人信息部分
      {
        field: 'personalInfo',
        label: '申请人信息',
        type: FiledOptions.DIVIDER,
        span: 24,
      },
      {
        field: 'personalInfo.name',
        label: '姓名',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入姓名',
      },
      {
        field: 'personalInfo.houseType',
        label: '申请人居住状况',
        type: FiledOptions.SELECT,
        span: 8,
        options: houseTypeOptions,
        placeholder: '请选择房产类型',
      },
      {
        field: 'personalInfo.cityCode',
        label: '申请人居住地址',
        type: FiledOptions.CUSTOM,
        span: 8,
        render: (props, ctx) => {
          return (
            <CitySelect
              value={props.value}
              change-on-select
              multiple={false}
              onUpdate:value={(value) => {
                ctx.emit('update:value', value);
              }}
            />
          );
        },
      },
      {
        field: 'personalInfo.residenceAddress',
        label: '申请人现居详细地址',
        type: FiledOptions.INPUT,
        span: 16,
        placeholder: '请输入申请人现居详细地址',
      },
      {
        field: 'personalInfo.monthlyIncome',
        label: '税后月收入',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请选择税后月收入',
      },
      {
        field: 'personalInfo.educationLevel',
        label: '申请人学历情况',
        type: FiledOptions.SELECT,
        span: 8,
        options: educationLevelOptions,
        placeholder: '请选择申请人学历情况',
      },
      {
        field: 'personalInfo.loanPurpose',
        label: '申请用途',
        type: FiledOptions.SELECT,
        span: 8,
        options: loanPurposeOptions,
        placeholder: '请选择申请用途',
      },
      {
        field: 'personalInfo.mateMobile',
        label: '配偶身份证号',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入配偶身份证号',
        componentProps: {
          maxlength: 18,
        },
        rules: [
          {
            validator: (_rule: any, value: any, callback: (error?: string | Error) => void) => {
              if (value) {
                const result = validIdCard(_rule, value);
                if (result instanceof Error) {
                  callback(result);
                } else {
                  callback();
                }
              } else {
                callback();
              }
            },
            trigger: ['blur', 'input', 'change'],
          },
        ],
      },
      {
        field: 'personalInfo.maritalStatus',
        label: '婚姻状况',
        type: FiledOptions.SELECT,
        span: 8,
        options: maritalStatusOptions,
        placeholder: '请选择婚姻状况',
      },
      {
        field: 'personalInfo.mateName',
        label: '配偶姓名',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入配偶姓名',
      },
      {
        field: 'personalInfo.mateMobile',
        label: '配偶手机号',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入配偶手机号',
      },
      // 联系人信息
      {
        field: 'contactInfo',
        label: '联系人信息',
        type: FiledOptions.DIVIDER,
        span: 24,
      },
      {
        field: 'personalInfo.contactRelation1',
        label: '联系人关系1',
        tooltip: '直系亲属',
        type: FiledOptions.SELECT,
        span: 8,
        options: contactRelationOptions,
        placeholder: '请选择联系人关系1',
      },
      {
        field: 'personalInfo.contactName1',
        label: '联系人1姓名',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入联系人1姓名',
      },
      {
        field: 'personalInfo.contactPhone1',
        label: '联系人1号码',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入联系人1号码',
        rules: [
          {
            validator: (_rule: any, value: any, callback: (error?: string | Error) => void) => {
              if (value) {
                const result = validPhone(_rule, value);
                if (result instanceof Error) {
                  callback(result);
                } else {
                  callback();
                }
              } else {
                callback();
              }
            },
            trigger: ['blur', 'input', 'change'],
          },
        ],
      },
      {
        field: 'personalInfo.contactRelation2',
        label: '联系人关系2',
        type: FiledOptions.SELECT,
        span: 8,
        options: contactRelationOptions1,
        placeholder: '请选择联系人关系2',
      },
      {
        field: 'personalInfo.contactName2',
        label: '联系人2姓名',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入联系人2姓名',
      },
      {
        field: 'personalInfo.contactPhone2',
        label: '联系人2号码',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入联系人2号码',
        rules: [
          {
            validator: (_rule: any, value: any, callback: (error?: string | Error) => void) => {
              if (value) {
                const result = validPhone(_rule, value);
                if (result instanceof Error) {
                  callback(result);
                } else {
                  callback();
                }
              } else {
                callback();
              }
            },
            trigger: ['blur', 'input', 'change'],
          },
        ],
      },

      // {
      //   field: 'personalInfo.mateWorkCompany',
      //   label: '配偶工作单位名称',
      //   type: FiledOptions.INPUT,
      //   span: 8,
      //   placeholder: '请输入配偶工作单位名称',
      // },
      // {
      //   field: 'personalInfo.mateWorkCompanyCity',
      //   label: '配偶工作单位地址',
      //   type: FiledOptions.CUSTOM,
      //   span: 8,
      //   render: (props, ctx) => {
      //     return (
      //       <CitySelect
      //         value={props.value}
      //         change-on-select
      //         multiple={false}
      //         onUpdate:value={(value) => {
      //           ctx.emit('update:value', value);
      //         }}
      //       />
      //     );
      //   },
      // },
      // {
      //   field: 'personalInfo.mateWorkCompanyAddress',
      //   label: '工作单位详细地址',
      //   type: FiledOptions.INPUT,
      //   span: 8,
      //   placeholder: '请输入工作单位详细地址',
      // },

      // {
      //   field: 'personalInfo.childCount',
      //   label: '子女数量',
      //   type: FiledOptions.SELECT,
      //   span: 8,
      //   options: childCountOptions,
      //   placeholder: '请选择子女数量',
      // },

      //工作信息
      {
        field: 'workInfo',
        label: '工作信息',
        type: FiledOptions.DIVIDER,
        span: 24,
      },
      {
        field: 'personalInfo.occupation',
        label: '申请人就业情况',
        type: FiledOptions.SELECT,
        span: 8,
        options: occupationOptions,
        placeholder: '请选择申请人就业情况',
      },
      {
        field: 'personalInfo.workType',
        label: '申请人所属行业',
        type: FiledOptions.SELECT,
        span: 8,
        options: workTypeOptions,
        placeholder: '请选择申请人所属行业',
      },
      {
        field: 'personalInfo.companyNature',
        label: '申请人单位性质',
        type: FiledOptions.SELECT,
        span: 8,
        options: companyNatureOptions,
        placeholder: '请选择申请人单位性质',
      },
      {
        field: 'personalInfo.company',
        label: '申请人单位名称',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入申请人单位名称',
      },
      {
        field: 'personalInfo.companyCityCode',
        label: '申请人单位地址',
        type: FiledOptions.CUSTOM,
        span: 8,
        render: (props, ctx) => {
          return (
            <CitySelectV2
              value={props.value}
              modelValue={props.value}
              change-on-select
              multiple={false}
              showDistrict={true}
              onUpdate:value={(value) => {
                ctx.emit('update:value', value);
              }}
            />
          );
        },
      },
      {
        field: 'personalInfo.companyAddress',
        label: '申请人单位详细地址',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入申请人单位详细地址',
      },

      // 证照信息部分
      {
        field: 'certificateInfo',
        label: '附件资料',
        type: FiledOptions.DIVIDER,
        span: 24,
      },
      {
        field: 'certificateInfo.idCardImgUrl',
        label: '身份证正面',
        labelPlacement: 'top',
        labelAlign: 'left',
        type: FiledOptions.UPLOAD,
        span: 4,
        componentProps: {
          accept: '.jpg,.jpeg,.png,.pdf',
        },
      },
      {
        field: 'certificateInfo.idCardImgBackUrl',
        label: '身份证背面',
        type: FiledOptions.UPLOAD,
        labelPlacement: 'top',
        labelAlign: 'left',
        span: 4,
        componentProps: {
          accept: '.jpg,.jpeg,.png,.pdf',
        },
      },
      {
        field: 'certificateInfo.drivingLicenseImgUrl',
        label: '行驶证',
        type: FiledOptions.UPLOAD,
        labelPlacement: 'top',
        labelAlign: 'left',
        span: 4,
        componentProps: {
          accept: '.jpg,.jpeg,.png,.pdf',
        },
      },
      // {
      //   field: 'certificateInfo.drivingLicenseImgBackUrl',
      //   label: '行驶证背面',
      //   type: FiledOptions.UPLOAD,
      //   labelPlacement: 'top',
      //   labelAlign: 'left',
      //   span: 4,
      //   componentProps: {
      //     accept: '.jpg,.jpeg,.png,.pdf',
      //   },
      // },
      {
        field: 'certificateInfo.paperDriverLicenseImgUrl',
        label: '驾驶证',
        type: FiledOptions.UPLOAD,
        labelPlacement: 'top',
        labelAlign: 'left',
        span: 4,
        componentProps: {
          accept: '.jpg,.jpeg,.png,.pdf',
        },
      },
      // {
      //   field: 'certificateInfo.paperDriverLicenseImgBackUrl',
      //   label: '驾驶证背面',
      //   type: FiledOptions.UPLOAD,
      //   labelPlacement: 'top',
      //   labelAlign: 'left',
      //   span: 4,
      //   componentProps: {
      //     accept: '.jpg,.jpeg,.png,.pdf',
      //   },
      // },
      {
        field: 'certificateInfo.vehicleRegistrationImgUrl',
        label: '车辆登记证(2)',
        type: FiledOptions.UPLOAD,
        labelPlacement: 'top',
        labelAlign: 'left',
        span: 8,
        componentProps: {
          accept: '.jpg,.jpeg,.png,.pdf',
          multiple: true,
          maxCount: 2,
        },
      },
      {
        field: 'certificateInfo.odographImgUrl',
        label: '里程表',
        type: FiledOptions.UPLOAD,
        labelPlacement: 'top',
        labelAlign: 'left',
        span: 4,
        componentProps: {
          accept: '.jpg,.jpeg,.png,.pdf',
        },
      },

      {
        field: 'certificateInfo.vehicleStatusImgUrl',
        label: '机动车状态（12123截图）',
        type: FiledOptions.UPLOAD,
        labelPlacement: 'top',
        labelAlign: 'left',
        span: 6,
        componentProps: {
          accept: '.jpg,.jpeg,.png,.pdf',
        },
      },
      // {
      //   field: 'certificateInfo.bankCardImgUrl',
      //   label: '银行卡正面',
      //   type: FiledOptions.UPLOAD,
      //   labelPlacement: 'top',
      //   labelAlign: 'left',
      //   span: 4,
      //   componentProps: {
      //     accept: '.jpg,.jpeg,.png,.pdf',
      //   },
      // },
      // {
      //   field: 'certificateInfo.bankCardBackUrl',
      //   label: '银行卡背面',
      //   type: FiledOptions.UPLOAD,
      //   labelPlacement: 'top',
      //   labelAlign: 'left',
      //   span: 4,
      //   componentProps: {
      //     accept: '.jpg,.jpeg,.png,.pdf',
      //   },
      // },

      {
        field: 'otherDocuments.vehiclePhotosLeft45',
        label: '车左前45°',
        type: FiledOptions.UPLOAD,
        labelPlacement: 'top',
        labelAlign: 'left',
        span: 4,
        componentProps: {
          accept: '.jpg,.jpeg,.png,.pdf',
        },
      },
      {
        field: 'otherDocuments.vehiclePhotoRight45',
        label: '车身右后方45°',
        type: FiledOptions.UPLOAD,
        labelPlacement: 'top',
        labelAlign: 'left',
        span: 4,
        componentProps: {
          accept: '.jpg,.jpeg,.png,.pdf',
        },
      },
      {
        field: 'otherDocuments.vehiclePhotoPeopleCar',
        label: '人车合影',
        type: FiledOptions.UPLOAD,
        labelPlacement: 'top',
        labelAlign: 'left',
        span: 4,
        componentProps: {
          accept: '.jpg,.jpeg,.png,.pdf',
        },
      },
      // {
      //   field: 'otherDocuments.vehiclePhotoFront',
      //   label: '车头全景',
      //   type: FiledOptions.UPLOAD,
      //   labelPlacement: 'top',
      //   labelAlign: 'left',
      //   span: 4,
      //   componentProps: {
      //     accept: '.jpg,.jpeg,.png,.pdf',
      //   },
      // },
    ],
  }));

  return {
    linkageFormRef,
    linkageFormData,
    linkageFormConfig,
  };
}
